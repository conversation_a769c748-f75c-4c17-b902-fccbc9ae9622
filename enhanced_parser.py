#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强版文言实词解析器
进一步优化例句释义的解析
"""

import json
import re
import sys
from typing import List, Dict, Any, Optional
import PyPDF2
import pdfplumber
from pathlib import Path

class EnhancedWenyanParser:
    """增强版文言实词解析器"""
    
    def __init__(self):
        self.words_data = []
        self.debug_mode = True
        
        # 定义常见的词性
        self.part_of_speech = [
            '名词', '动词', '形容词', '副词', '介词', '连词', '助词', '叹词',
            '代词', '数词', '量词', '语气词', '象声词'
        ]
    
    def extract_text_from_pdf(self, pdf_path: str) -> str:
        """从PDF提取文本内容"""
        text = ""
        
        # 优先使用pdfplumber
        try:
            with pdfplumber.open(pdf_path) as pdf:
                for page_num, page in enumerate(pdf.pages):
                    page_text = page.extract_text()
                    if page_text:
                        # 清理页眉页脚
                        cleaned_text = self.clean_page_text(page_text)
                        text += cleaned_text + "\n"
                        if self.debug_mode and page_num < 5:  # 只显示前5页的调试信息
                            print(f"第{page_num+1}页提取了{len(page_text)}字符，清理后{len(cleaned_text)}字符")
            
            if text:
                print(f"使用pdfplumber成功提取文本，共{len(text)}字符")
                return text
        except Exception as e:
            print(f"pdfplumber提取失败: {e}")
        
        return ""
    
    def clean_page_text(self, page_text: str) -> str:
        """清理单页文本，去除页眉页脚"""
        lines = page_text.split('\n')
        cleaned_lines = []
        
        for line in lines:
            line = line.strip()
            
            # 跳过页眉：包含"文言实词300个"的行
            if '文言实词300个' in line or '文言实词 300 个' in line:
                continue
            
            # 跳过页眉：只有横线的行
            if re.match(r'^[-—_=]+$', line):
                continue
            
            # 跳过页脚：只有数字的行（页码）
            if re.match(r'^\d+$', line):
                continue
            
            # 跳过空行
            if not line:
                continue
            
            cleaned_lines.append(line)
        
        return '\n'.join(cleaned_lines)
    
    def split_into_word_entries(self, text: str) -> List[str]:
        """将文本分割为单个实词条目"""
        # 保存原始文本用于调试
        if self.debug_mode:
            with open("debug_enhanced_text.txt", 'w', encoding='utf-8') as f:
                f.write(text)
        
        # 查找所有可能的词条分割点
        patterns = [
            r'\n(\d+[、．]\s*[\u4e00-\u9fff]+)',  # 1、昂
        ]
        
        # 找到所有分割点
        split_points = []
        for pattern in patterns:
            matches = re.finditer(pattern, text)
            for match in matches:
                split_points.append((match.start(), match.group(1)))
        
        # 按位置排序
        split_points.sort(key=lambda x: x[0])
        
        if self.debug_mode:
            print(f"找到 {len(split_points)} 个词条分割点")
        
        # 按分割点分割
        if split_points:
            entries = []
            for i, (pos, _) in enumerate(split_points):
                start_pos = pos
                end_pos = split_points[i+1][0] if i+1 < len(split_points) else len(text)
                entry = text[start_pos:end_pos].strip()
                if entry and len(entry) > 10:
                    entries.append(entry)
        else:
            print("未找到标准分割点")
            entries = []
        
        print(f"分割得到 {len(entries)} 个词条")
        return entries
    
    def extract_word_name(self, text: str) -> str:
        """提取实词名称"""
        lines = text.split('\n')
        first_line = lines[0].strip()
        
        # 匹配编号格式：1、昂
        match = re.match(r'^\d+[、．]\s*(.+)', first_line)
        if match:
            word = match.group(1).strip()
            # 只保留中文字符
            word = re.sub(r'[^\u4e00-\u9fff]', '', word)
            if word and len(word) <= 5:
                return word
        
        return ""
    
    def parse_usage_entries(self, text: str) -> List[Dict[str, str]]:
        """解析用法条目"""
        usages = []
        
        # 按圆圈数字分割用法，使用更精确的模式
        usage_pattern = r'([①②③④⑤⑥⑦⑧⑨⑩])\s*([^①②③④⑤⑥⑦⑧⑨⑩]+?)(?=\s*[①②③④⑤⑥⑦⑧⑨⑩]|$)'
        usage_matches = re.findall(usage_pattern, text, re.DOTALL)
        
        for circle_num, usage_text in usage_matches:
            usage = self.parse_single_usage(usage_text.strip())
            if usage and usage.get("含义"):
                usages.append(usage)
        
        return usages
    
    def parse_single_usage(self, usage_text: str) -> Optional[Dict[str, str]]:
        """解析单个用法"""
        usage = {
            "词性": "",
            "含义": "",
            "例句出处": "",
            "例句内容": "",
            "例句释义": ""
        }
        
        # 分离词性含义和例句部分
        example_split = re.split(r'\n?例[：:]', usage_text, 1)
        
        # 解析词性和含义
        first_part = example_split[0].strip()
        self.parse_part_of_speech_and_meaning(usage, first_part)
        
        # 解析例句部分
        if len(example_split) > 1:
            example_part = example_split[1].strip()
            self.parse_example_comprehensive(usage, example_part)
        
        return usage if usage["含义"] else None
    
    def parse_part_of_speech_and_meaning(self, usage: Dict[str, str], text: str):
        """解析词性和含义"""
        # 尝试识别词性
        for pos in self.part_of_speech:
            if text.startswith(pos):
                usage["词性"] = pos
                remaining = text[len(pos):].strip()
                if remaining.startswith('，') or remaining.startswith(','):
                    remaining = remaining[1:].strip()
                usage["含义"] = remaining
                return
        
        # 如果没有识别到词性，整个作为含义
        usage["含义"] = text
    
    def parse_example_comprehensive(self, usage: Dict[str, str], example_text: str):
        """综合解析例句"""
        # 先查找书名号
        book_match = re.search(r'《([^》]+)》(.+)', example_text, re.DOTALL)
        if book_match:
            usage["例句出处"] = f"《{book_match.group(1)}》"
            remaining_text = book_match.group(2).strip()
        else:
            remaining_text = example_text.strip()
        
        # 分离例句内容和释义
        self.separate_sentence_and_explanation(usage, remaining_text)
    
    def separate_sentence_and_explanation(self, usage: Dict[str, str], text: str):
        """分离例句内容和释义"""
        # 方法1：查找明确的括号分隔
        paren_match = re.search(r'^([^（(]*?)[（(]([^）)]+)[）)](.*)$', text, re.DOTALL)
        if paren_match:
            usage["例句内容"] = paren_match.group(1).strip()
            explanation = paren_match.group(2).strip()
            remaining = paren_match.group(3).strip()
            if remaining:
                explanation += remaining
            usage["例句释义"] = explanation
            return
        
        # 方法2：按换行分割，第一行是例句，后续是释义
        lines = [line.strip() for line in text.split('\n') if line.strip()]
        if len(lines) >= 2:
            usage["例句内容"] = lines[0]
            # 合并后续行作为释义，过滤掉可能的页眉页脚
            explanation_lines = []
            for line in lines[1:]:
                # 跳过可能的页码或标题
                if re.match(r'^\d+$', line) or '文言实词' in line:
                    continue
                explanation_lines.append(line)
            
            if explanation_lines:
                usage["例句释义"] = ' '.join(explanation_lines)
            return
        
        # 方法3：如果只有一行，尝试智能分割
        if len(lines) == 1:
            line = lines[0]
            # 查找可能的分割点（如句号后的解释）
            sentence_end_match = re.search(r'^([^。！？]*[。！？])(.+)$', line)
            if sentence_end_match:
                usage["例句内容"] = sentence_end_match.group(1).strip()
                usage["例句释义"] = sentence_end_match.group(2).strip()
                return
        
        # 如果都没有找到合适的分割，整个作为例句内容
        usage["例句内容"] = text.strip()
    
    def parse_word_entry(self, word_text: str, word_id: int) -> Dict[str, Any]:
        """解析单个实词条目"""
        word_data = {
            "id": word_id,
            "实词": "",
            "用法": []
        }
        
        # 提取实词名称
        word_name = self.extract_word_name(word_text)
        if not word_name:
            return word_data
        
        word_data["实词"] = word_name
        
        # 解析用法
        usages = self.parse_usage_entries(word_text)
        word_data["用法"] = usages
        
        return word_data

    def parse_pdf_to_json(self, pdf_path: str, output_path: str = None, max_words: int = None):
        """主函数：解析PDF并转换为JSON"""
        print(f"开始处理PDF文件: {pdf_path}")

        # 提取PDF文本
        text = self.extract_text_from_pdf(pdf_path)
        if not text:
            print("无法从PDF提取文本内容")
            return

        # 分割为词条
        word_entries = self.split_into_word_entries(text)

        # 限制处理数量（用于测试）
        if max_words:
            word_entries = word_entries[:max_words]

        # 解析每个词条
        for i, entry in enumerate(word_entries):
            word_id = i + 1  # ID从1开始
            print(f"\n处理第 {word_id} 个词条:")
            if self.debug_mode:
                print(f"原文: {entry[:100]}...")

            word_data = self.parse_word_entry(entry, word_id)
            if word_data["实词"]:
                self.words_data.append(word_data)
                print(f"解析结果: ID={word_id}, {word_data['实词']} - {len(word_data['用法'])}种用法")
            else:
                print("跳过：无法识别的词条格式")

        # 输出JSON
        if not output_path:
            output_path = "wenyan_words_enhanced.json"

        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(self.words_data, f, ensure_ascii=False, indent=2)

        print(f"\n解析完成！共处理 {len(self.words_data)} 个实词")
        print(f"结果已保存到: {output_path}")

        # 显示示例
        if self.words_data:
            print(f"\n示例数据:")
            print(json.dumps(self.words_data[0], ensure_ascii=False, indent=2))

        return self.words_data

def main():
    """主程序入口"""
    if len(sys.argv) < 2:
        print("使用方法: python enhanced_parser.py <PDF文件路径> [输出JSON文件路径] [最大词数]")
        print("示例: python enhanced_parser.py 文言实词300个最新.pdf wenyan_words.json 10")
        return

    pdf_path = sys.argv[1]
    output_path = sys.argv[2] if len(sys.argv) > 2 else None
    max_words = int(sys.argv[3]) if len(sys.argv) > 3 else None

    if not Path(pdf_path).exists():
        print(f"错误: PDF文件不存在: {pdf_path}")
        return

    parser = EnhancedWenyanParser()
    parser.parse_pdf_to_json(pdf_path, output_path, max_words)

if __name__ == "__main__":
    main()
