#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查文言实词JSON数据的完整性和质量
"""

import json
from collections import defaultdict

def check_data_quality(json_file):
    """检查JSON数据的质量和完整性"""
    
    with open(json_file, 'r', encoding='utf-8') as f:
        words_data = json.load(f)
    
    print(f"=== 数据质量检查报告 ===")
    print(f"文件: {json_file}")
    print(f"总词条数: {len(words_data)}")
    
    # 统计各种缺失情况
    missing_stats = {
        'id': 0,
        '实词': 0,
        '用法': 0,
        '词性': 0,
        '含义': 0,
        '例句出处': 0,
        '例句内容': 0,
        '例句释义': 0
    }
    
    # 记录有问题的词条
    problematic_words = []
    
    for i, word_data in enumerate(words_data):
        word_id = word_data.get('id', '')
        word_name = word_data.get('实词', '')
        usages = word_data.get('用法', [])
        
        # 检查顶级字段
        if not word_id:
            missing_stats['id'] += 1
            problematic_words.append(f"词条{i+1}: 缺少ID")
        
        if not word_name:
            missing_stats['实词'] += 1
            problematic_words.append(f"词条{i+1}: 缺少实词名称")
        
        if not usages:
            missing_stats['用法'] += 1
            problematic_words.append(f"词条{i+1} ({word_name}): 没有用法")
            continue
        
        # 检查每个用法的字段
        for j, usage in enumerate(usages):
            usage_id = f"词条{word_id} ({word_name}) 用法{j+1}"
            
            if not usage.get('词性', '').strip():
                missing_stats['词性'] += 1
                problematic_words.append(f"{usage_id}: 缺少词性")
            
            if not usage.get('含义', '').strip():
                missing_stats['含义'] += 1
                problematic_words.append(f"{usage_id}: 缺少含义")
            
            if not usage.get('例句出处', '').strip():
                missing_stats['例句出处'] += 1
                problematic_words.append(f"{usage_id}: 缺少例句出处")
            
            if not usage.get('例句内容', '').strip():
                missing_stats['例句内容'] += 1
                problematic_words.append(f"{usage_id}: 缺少例句内容")
            
            if not usage.get('例句释义', '').strip():
                missing_stats['例句释义'] += 1
                problematic_words.append(f"{usage_id}: 缺少例句释义")
    
    # 输出统计结果
    print(f"\n=== 缺失字段统计 ===")
    total_usages = sum(len(word.get('用法', [])) for word in words_data)
    print(f"总用法数: {total_usages}")
    
    for field, count in missing_stats.items():
        if field in ['id', '实词', '用法']:
            total = len(words_data)
        else:
            total = total_usages
        
        percentage = (count / total * 100) if total > 0 else 0
        print(f"{field}: {count}/{total} ({percentage:.1f}%)")
    
    # 输出有问题的词条（前20个）
    if problematic_words:
        print(f"\n=== 有问题的词条 (前20个) ===")
        for i, problem in enumerate(problematic_words[:20]):
            print(f"{i+1}. {problem}")
        
        if len(problematic_words) > 20:
            print(f"... 还有 {len(problematic_words) - 20} 个问题")
    
    # 检查特殊情况
    print(f"\n=== 特殊情况检查 ===")
    
    # 检查词性为空的情况
    empty_pos_count = 0
    for word_data in words_data:
        for usage in word_data.get('用法', []):
            if not usage.get('词性', '').strip():
                empty_pos_count += 1
    
    print(f"词性为空的用法: {empty_pos_count}")
    
    # 检查例句释义为空的情况
    empty_explanation_words = []
    for word_data in words_data:
        word_name = word_data.get('实词', '')
        for j, usage in enumerate(word_data.get('用法', [])):
            if not usage.get('例句释义', '').strip():
                empty_explanation_words.append(f"{word_name} 用法{j+1}")
    
    print(f"例句释义为空的用法: {len(empty_explanation_words)}")
    if empty_explanation_words:
        print("缺少例句释义的词条:")
        for i, word_usage in enumerate(empty_explanation_words[:10]):
            print(f"  {i+1}. {word_usage}")
        if len(empty_explanation_words) > 10:
            print(f"  ... 还有 {len(empty_explanation_words) - 10} 个")
    
    # 检查例句出处格式
    print(f"\n=== 例句出处格式检查 ===")
    invalid_source_count = 0
    for word_data in words_data:
        for usage in word_data.get('用法', []):
            source = usage.get('例句出处', '')
            if source and not (source.startswith('《') and source.endswith('》')):
                invalid_source_count += 1
    
    print(f"例句出处格式不正确: {invalid_source_count}")
    
    return {
        'total_words': len(words_data),
        'total_usages': total_usages,
        'missing_stats': missing_stats,
        'problematic_count': len(problematic_words)
    }

def show_sample_data(json_file, word_id=None):
    """显示示例数据"""
    with open(json_file, 'r', encoding='utf-8') as f:
        words_data = json.load(f)
    
    if word_id:
        # 显示指定ID的词条
        for word_data in words_data:
            if word_data.get('id') == word_id:
                print(f"\n=== 词条 {word_id} 详细信息 ===")
                print(json.dumps(word_data, ensure_ascii=False, indent=2))
                return
        print(f"未找到ID为 {word_id} 的词条")
    else:
        # 显示前3个词条
        print(f"\n=== 前3个词条示例 ===")
        for i, word_data in enumerate(words_data[:3]):
            print(f"\n--- 词条 {i+1} ---")
            print(json.dumps(word_data, ensure_ascii=False, indent=2))

if __name__ == "__main__":
    # 检查数据质量
    result = check_data_quality("wenyan_words_enhanced.json")
    
    # 显示示例数据
    show_sample_data("wenyan_words_enhanced.json")
    
    # 如果有问题，显示一些有问题的词条
    if result['problematic_count'] > 0:
        print(f"\n=== 建议检查的词条 ===")
        show_sample_data("wenyan_words_enhanced.json", 50)  # 显示第50个词条
        show_sample_data("wenyan_words_enhanced.json", 100)  # 显示第100个词条
